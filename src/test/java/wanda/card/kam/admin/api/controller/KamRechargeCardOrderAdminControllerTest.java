package wanda.card.kam.admin.api.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import wanda.card.kam.admin.api.ApiApplication;

@SpringBootTest(classes = {ApiApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class KamRechargeCardOrderAdminControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void list() {
        restTemplate.postForObject("/card-kam/recharge-order/list", null, String.class);
    }

    @Test
    void export() {
    }

    @Test
    void detail() {
    }

    @Test
    void cardDetailList() {
    }
}