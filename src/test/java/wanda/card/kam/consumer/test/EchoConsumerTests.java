package wanda.card.kam.consumer.test;

import wanda.card.kam.consumer.ConsumerApplication;
import wanda.card.kam.consumer.consumers.EchoConsumer;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.stark.core.test.TestBase;

@SpringBootTest(classes = {ConsumerApplication.class})
class EchoConsumerTests extends TestBase{

    @Autowired
    private EchoConsumer consumer;

    @Test
    public void testEcho() {
        String value = "{\"movieId\": 1000}";
        consumer.process(value, null);
    }
}