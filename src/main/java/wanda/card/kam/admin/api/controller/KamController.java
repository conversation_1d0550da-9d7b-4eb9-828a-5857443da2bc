package wanda.card.kam.admin.api.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyResponse;
import wanda.card.kam.admin.api.model.RechargeOrderPushModel.RechargeOrderPushRequest;
import wanda.card.kam.admin.api.model.RechargeOrderPushModel.RechargeOrderPushResponse;
import wanda.stark.core.data.Result;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("kam")
@AllArgsConstructor
public class KamController {

    /**
     * 充值验证
     */
    @PostMapping(value = "recharge/verify")
    public Result<VerifyResponse> rechargeVerify(@RequestBody VerifyRequest request) {
        //批量查询卡信息
        //校验卡状态，客户信息
        //校验卡类型是否可充值，最低充值金额限制
        return Result.success();
    }

    /**
     * @api {POST} /kam/recharge/order/push 充值订单推送接口
     * @apiVersion 1.0.0
     * @apiGroup 财管系统使用的api
     * @apiName RechargeOrderPush
     * @apiDescription 财管合同审批通过后推送充值订单到万达云，推送信息包含充值订单号、卡号、充值金额、赠送金额等字段
     * @apiParam (请求体) {String} rechargeOrderNo 充值订单号（必填）
     * @apiParam (请求体) {String} contractNo 合同编码（必填）
     * @apiParam (请求体) {String} customerCode 客户编码（必填）
     * @apiParam (请求体) {String} customerName 客户名称（必填）
     * @apiParam (请求体) {String} sellerId 销售员万信号（必填）
     * @apiParam (请求体) {String} seller 销售员姓名（必填）
     * @apiParam (请求体) {String} areaCode 区域编码（必填）
     * @apiParam (请求体) {String} areaName 区域名称（必填）
     * @apiParam (请求体) {String} cinemaInnerCode 影城内码（必填）
     * @apiParam (请求体) {String} cinemaName 影城名称（必填）
     * @apiParam (请求体) {Array} cardInfos 卡信息列表（必填）
     * @apiParam (请求体) {String} cardInfos.cardNo 卡号（必填）
     * @apiParam (请求体) {String} cardInfos.cardTypeCode 卡类型编码（必填）
     * @apiParam (请求体) {String} cardInfos.cardTypeName 卡类型名称（必填）
     * @apiParam (请求体) {Number} cardInfos.rechargeAmount 充值金额（分）（必填）
     * @apiParam (请求体) {Number} cardInfos.presentAmount 赠送金额（分）（必填）
     * @apiParamExample 请求体示例
     * {
     *   "rechargeOrderNo": "RO202501010001",
     *   "contractNo": "CT202501010001",
     *   "customerCode": "CU001",
     *   "customerName": "测试客户",
     *   "sellerId": "S001",
     *   "seller": "张三",
     *   "areaCode": "A001",
     *   "areaName": "华北区",
     *   "cinemaInnerCode": "C001",
     *   "cinemaName": "万达影城测试店",
     *   "cardInfos": [
     *     {
     *       "cardNo": "1234567890123456",
     *       "cardTypeCode": "120000121",
     *       "cardTypeName": "储值卡",
     *       "rechargeAmount": 30000,
     *       "presentAmount": 0
     *     }
     *   ]
     * }
     * @apiSuccess (响应结果) {Number} code 0:成功 非0:失败
     * @apiSuccess (响应结果) {String} msg 失败提示信息
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {String} data.message 推送结果消息
     * @apiSuccess (响应结果) {String} data.rechargeOrderNo 充值订单号
     * @apiSuccess (响应结果) {Number} data.cardCount 处理的卡数量
     * @apiSuccess (响应结果) {Number} data.totalAmount 总充值金额（分）
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": 0,
     *   "msg": "成功",
     *   "data": {
     *     "message": "充值订单推送成功",
     *     "rechargeOrderNo": "RO202501010001",
     *     "cardCount": 1,
     *     "totalAmount": 30000
     *   }
     * }
     */
    @PostMapping(value = "recharge/order/push")
    public Result<RechargeOrderPushResponse> rechargeOrderPush(@Valid @RequestBody RechargeOrderPushRequest request) {
        log.info("充值订单推送入参:{}", request);

        // TODO: 实现充值订单推送逻辑
        // 1. 验证充值订单号是否已存在
        // 2. 保存充值订单基本信息到recharge_orders表
        // 3. 保存卡信息到recharge_order_card_infos表
        // 4. 记录操作日志到recharge_order_logs表

        // 计算总金额和卡数量
        int cardCount = request.getCardInfos().size();
        int totalAmount = request.getCardInfos().stream()
                .mapToInt(card -> card.getRechargeAmount() + card.getPresentAmount())
                .sum();

        RechargeOrderPushResponse response = new RechargeOrderPushResponse(
                "充值订单推送成功",
                request.getRechargeOrderNo(),
                cardCount,
                totalAmount
        );

        log.info("充值订单推送返回:{}", response);
        return Result.success(response);
    }
}