package wanda.card.kam.consumer.executor;

import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import wanda.stark.msg.subscriber.AbstractConsumer;
import wanda.stark.msg.subscriber.MessageConsumer;

@MessageConsumer(topic = "echo_test_topic")
public class EchoConsumer extends AbstractConsumer<String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(EchoConsumer.class);

    @Override
    public void process(String body, MessageExt origin) {
        LOGGER.info("Hello, received message: {}", body);
    }
}
