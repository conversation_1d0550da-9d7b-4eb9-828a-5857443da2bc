# 示例

`EchoService.echo(request)`

# 测试

`curl -X POST -H "Content-Type:application/json" -d "{\"name\":\"everyone\"}" "http://localhost:8080/echo"`

# 配置

> 目前服务名字有2处配置，示例服务快速生成骨架的命名规则：${package}-${rootArtifactId}-service

## contract包

```java
@Configuration
@EnableFeignClients
public class ServiceAutoConfiguration {
    public static final String APPLICATION_NAME = "wanda.card.common-card-kam-common-service";
}
```

## src/main/resources/application.yml

应用自身需要的配置，用来拉取远程配置(nacos)

`spring.application.name=wanda.card.common-card-kam-common-service`