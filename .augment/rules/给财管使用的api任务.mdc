# 财管API需求
- 说明：数据保存在mysql里，订单数据保存在单库单表，相关实体已放到card-kam-common模块里

## 1. 充值订单推送接口-产研提供
- 接口定义在card-kam-admin-api模块KamController里
- 具体逻辑实现在card-kam-admin-service里用rpc接口实现，推送相关逻辑放到biz包下，需考虑性能用批量写入方法，KamController接口依赖这个rpc接口
- dao、实体和共用逻辑在card-kam-common模块里实现

## 2. 充值订单充值接口-产研提供
- 接口定义在card-kam-admin-api模块KamController里
- 具体逻辑实现在card-kam-admin-service里rpc接口实现。KamController接口依赖这个rpc接口
- rpc接口逻辑1.根据传来的卡号先校验过滤出待充值和充值失败的卡，将其批量设置为充值中状态，log记录充值人等，逻辑1外出应该套一层以订单号为key的redis自旋锁
- rpc接口逻辑2.会异步发送消息给card-kam-consumer模块，由消费者处理具体充值逻辑，消息体传订单号
- card-kam-consumer会调用卡系统卡充值rpc接口，为每张卡依次充值
- 在使用充值rpc接口为每张卡充值前，需要批量判断销售单卡状态，如果该卡是充值中或充值成功，则不予充值跳过，否则将充值订单的卡状态值为充值中调用充值rpc接口为该卡充值且
- dao、实体和共用逻辑在card-kam-common模块里实现