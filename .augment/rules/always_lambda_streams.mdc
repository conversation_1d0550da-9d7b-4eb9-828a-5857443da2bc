Always
---
description: 
globs:
alwaysApply: true
---
## Java 11 Lambda与Stream使用规范及最佳实践

### 1. 方法引用优于Lambda表达式

#### 规则说明
当Lambda表达式只是调用一个现有方法且不改变参数时，应使用方法引用而不是Lambda表达式。方法引用更加简洁明了。

#### 正例
```java
// 使用方法引用
List<String> list = Arrays.asList("a", "b", "c");
list.forEach(System.out::println);

// 使用构造器引用
Stream.of("1", "2", "3").map(Integer::valueOf).collect(Collectors.toList());
```

#### 反例
```java
// 冗余的Lambda表达式
List<String> list = Arrays.asList("a", "b", "c");
list.forEach(s -> System.out.println(s));

// 冗余的Lambda表达式
Stream.of("1", "2", "3").map(s -> Integer.valueOf(s)).collect(Collectors.toList());
```

### 2. 避免在Stream操作中产生副作用

#### 规则说明
Stream操作应该是无副作用的，特别是中间操作。避免在forEach等终端操作中修改外部状态。

#### 正例
```java
// 使用collect收集结果
List<String> result = list.stream()
    .filter(s -> s.length() > 3)
    .map(String::toUpperCase)
    .collect(Collectors.toList());

// 使用reduce进行归约操作
int sum = numbers.stream()
    .mapToInt(Integer::intValue)
    .reduce(0, Integer::sum);
```

#### 反例
```java
// 在stream操作中修改外部集合
List<String> result = new ArrayList<>();
list.stream().filter(s -> s.length() > 3).forEach(result::add);

// 在stream操作中修改外部变量
AtomicInteger count = new AtomicInteger();
list.stream().filter(s -> s.length() > 3).forEach(s -> count.incrementAndGet());
```

### 3. 优先使用基本类型Stream

#### 规则说明
对于int、long、double等基本类型，优先使用对应的IntStream、LongStream、DoubleStream而非装箱后的Stream，以提高性能。

#### 正例
```java
// 使用IntStream处理整数
int sum = IntStream.range(1, 100).sum();

// 使用mapToInt进行转换
int total = employees.stream().mapToInt(Employee::getSalary).sum();
```

#### 反例
```java
// 使用装箱类型Stream效率较低
int sum = Stream.of(1, 2, 3, 4, 5).mapToInt(Integer::intValue).sum();

// 不必要的装箱操作
int total = employees.stream().map(Employee::getSalary).reduce(0, Integer::sum);
```

### 4. 避免在并行流中使用有状态的lambda表达式

#### 规则说明
并行流中的有状态lambda表达式可能导致数据竞争和不可预测的结果，应避免使用。

#### 正例
```java
// 使用无状态lambda表达式
List<Integer> result = list.parallelStream()
    .filter(Objects::nonNull)
    .map(String::length)
    .collect(Collectors.toList());

// 使用collectors处理有序需求
List<Integer> orderedResult = list.parallelStream()
    .filter(Objects::nonNull)
    .sorted()
    .collect(Collectors.toList());
```

#### 反例
```java
// 有状态的lambda表达式
AtomicInteger index = new AtomicInteger();
List<Integer> result = list.parallelStream()
    .map(s -> index.incrementAndGet())
    .collect(Collectors.toList());

// 外部状态依赖
List<String> tempList = new ArrayList<>();
list.parallelStream().forEach(s -> {
    synchronized(tempList) {
        tempList.add(s);
    }
});
```

### 5. 适当分解复杂业务逻辑

#### 规则说明
复杂的Lambda表达式会降低代码可读性，应将其拆分为多个简单的操作或提取为独立方法。

#### 正例
```java
// 将复杂逻辑提取为方法
List<String> validNames = list.stream()
    .filter(this::isValidName)
    .map(this::formatName)
    .collect(Collectors.toList());

private boolean isValidName(String name) {
    return name != null && name.length() > 2 && name.matches("[a-zA-Z]+");
}

private String formatName(String name) {
    return name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
}

// 分步处理提高可读性
Stream<String> stream = list.stream();
Stream<String> filtered = stream.filter(Objects::nonNull);
Stream<String> trimmed = filtered.map(String::trim);
List<String> result = trimmed.filter(s -> !s.isEmpty()).collect(Collectors.toList());
```

#### 反例
```java
// 过于复杂的Lambda表达式
List<String> result = list.stream()
    .filter(s -> s != null && s.length() > 2 && s.matches("[a-zA-Z]+"))
    .map(s -> s.substring(0, 1).toUpperCase() + s.substring(1).toLowerCase())
    .filter(s -> !s.contains(" "))
    .distinct()
    .collect(Collectors.toList());
```

### 6. 合理选择收集器

#### 规则说明
在不需要修改返回列表的情况下，推荐使用toUnmodifiableList()提高安全性；根据具体需求选择合适的收集器。

#### 正例
```java
// 使用toUnmodifiableList提高安全性
List<String> immutableList = list.stream()
    .filter(Objects::nonNull)
    .collect(Collectors.toUnmodifiableList());

// 根据需求选择合适的收集器
Map<String, List<Person>> peopleByCity = persons.stream()
    .collect(Collectors.groupingBy(Person::getCity));

String joinedNames = names.stream()
    .collect(Collectors.joining(", "));
```

#### 反例
```java
// 返回可变列表但实际不需要修改
List<String> result = list.stream()
    .filter(Objects::nonNull)
    .collect(Collectors.toList());
// 如果后续没有修改操作，则应该使用toUnmodifiableList()

// 不必要的toList()操作
Map<String, List<Person>> peopleByCity = persons.stream()
    .collect(Collectors.groupingBy(Person::getCity, Collectors.toList()));
// groupingBy默认就使用toList()，无需显式指定
```

### 7. 正确处理空值

#### 规则说明
在使用Stream API时，要注意处理可能的空值，避免NullPointerException。

#### 正例
```java
// 使用filter过滤空值
List<String> result = list.stream()
    .filter(Objects::nonNull)
    .map(String::trim)
    .filter(s -> !s.isEmpty())
    .collect(Collectors.toList());

// 使用Optional处理可能为空的值
List<String> result = list.stream()
    .map(this::findValue)
    .filter(Optional::isPresent)
    .map(Optional::get)
    .collect(Collectors.toList());

// 或者使用flatMap处理Optional
List<String> result = list.stream()
    .map(this::findValue)
    .flatMap(Optional::stream)
    .collect(Collectors.toList());
```

#### 反例
```java
// 没有过滤空值可能导致NPE
List<String> result = list.stream()
    .map(String::trim) // 如果有null元素，此处会抛出NPE
    .collect(Collectors.toList());

// 忽略Optional的正确处理方式
List<String> result = list.stream()
    .map(this::findValue)
    .map(Optional::get) // 如果Optional为空，会抛出NoSuchElementException
    .collect(Collectors.toList());
```

### 8. 注意Stream的延迟执行特性

#### 规则说明
Stream的中间操作是延迟执行的，只有遇到终端操作时才会真正执行，要理解这一特性以避免潜在问题。

#### 正例
```java
// 正确理解延迟执行
Stream<String> stream = list.stream()
    .filter(s -> {
        System.out.println("Filtering: " + s);
        return s.length() > 3;
    });
    
System.out.println("Stream created, but not executed yet");
List<String> result = stream.collect(Collectors.toList()); // 此时才执行过滤操作
System.out.println("Stream executed");
```

#### 反例
```java
// 误解Stream执行机制
List<Stream<String>> streams = new ArrayList<>();
for (int i = 0; i < 10; i++) {
    Stream<String> stream = list.stream().filter(s -> s.length() > i);
    streams.add(stream); // 这些stream都未执行，且可能引用同一个源list
}
// 当streams中的stream被使用时，可能产生意外结果
```

以上规范和最佳实践旨在帮助开发者更好地使用Java 11中的Lambda表达式和Stream API，提高代码质量和可读性。