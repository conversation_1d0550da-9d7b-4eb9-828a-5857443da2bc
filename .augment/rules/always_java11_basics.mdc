Always
---
description: 
globs:
alwaysApply: true
---
本规则将应用于所有Java相关对话，确保基础一致性。

- 所有Java代码示例必须基于Java 11版本，禁止使用Java 11后新增的语法（如密封类`sealed`）和 模块化特性。
- 代码注释需遵循Javadoc规范，方法注释需包含`@param` `@return`（若有返回值）和`@throws`（若抛出异常）。
- 每次提供代码后，简要说明该代码如何利用Java 11特性（如`var`局部变量、`String.strip()`等）。


## Java 11最佳实践规范

### 语言特性使用规范

#### 局部变量类型推断（var关键字）
```java
// 推荐：在明确类型的情况下使用var
var memberList = new ArrayList<MemberDto>();
var memberNo = "12345";
var count = memberService.countByStatus(StatusEnum.ACTIVE);

// 不推荐：类型不明确的情况
var result = processData(); // 返回类型不明确
var value = null; // 无法推断类型
```

#### 字符串处理增强
```java
// 使用新的字符串方法
String content = "  hello world  ";
boolean isEmpty = content.isBlank(); // 检查空白字符
String trimmed = content.strip(); // 去除首尾空白
List<String> lines = content.lines().collect(Collectors.toList()); // 按行分割

// 重复字符串
String separator = "-".repeat(50);
```

#### 集合工厂方法
```java
// 创建不可变集合
List<String> statusList = List.of("ACTIVE", "INACTIVE", "PENDING");
Set<Integer> typeSet = Set.of(1, 2, 3, 4);
Map<String, String> configMap = Map.of(
    "timeout", "30000",
    "retryCount", "3"
);
```

#### Optional增强使用
```java
// 使用isEmpty()方法
Optional<MemberDto> memberOpt = memberService.findByNo(memberNo);
if (memberOpt.isEmpty()) {
    throw new BusinessException(ErrorCodeEnum.MEMBER_NOT_FOUND);
}

// 链式调用
String memberName = memberService.findByNo(memberNo)
    .map(MemberDto::getName)
    .orElse("未知用户");
```


### 性能优化规范

#### 垃圾收集器选择
```yaml
# application.yml - JVM参数配置
server:
  port: 8080
  
# JVM启动参数推荐
# -XX:+UseG1GC                    # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=200        # 最大GC暂停时间
# -XX:+UseStringDeduplication     # 启用字符串去重
# -XX:+UnlockExperimentalVMOptions # 解锁实验性选项
```

#### 内存管理最佳实践
```java
// 使用try-with-resources自动资源管理
public void processFile(String filePath) {
    try (var reader = Files.newBufferedReader(Paths.get(filePath));
         var writer = Files.newBufferedWriter(Paths.get("output.txt"))) {
        
        reader.lines()
            .filter(line -> !line.isBlank())
            .forEach(line -> {
                try {
                    writer.write(line + System.lineSeparator());
                } catch (IOException e) {
                    log.error("写入文件失败", e);
                }
            });
    } catch (IOException e) {
        log.error("文件处理失败", e);
    }
}
```

### 日期时间API规范

#### 使用新的时间API
```java
// 时间处理工具类
public class TimeUtil {
    private TimeUtil(){}

    public static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai");
    
    public static LocalDateTime now() {
        return LocalDateTime.now(CHINA_ZONE);
    }
    
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    public static boolean isExpired(LocalDateTime expireTime) {
        return expireTime.isBefore(now());
    }
}
```

### 安全编程规范

#### 输入验证
```java
// 参数验证
@Data
@AllArgsConstructor
public class MemberQueryParam {
    
    @NotBlank(message = "会员号不能为空")
    @Pattern(regexp = "^[0-9]{6,20}$", message = "会员号格式不正确")
    private String memberNo;
    
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer pageSize = 20;
}
```

#### 异常处理
```java
// 统一异常处理
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public R<Void> handleValidationException(ValidationException e) {
        log.warn("参数验证失败: {}", e.getMessage());
        return RMapper.toR(ErrorCodeEnum.PARAM_ERROR, e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return RMapper.toR(ErrorCodeEnum.SYSTEM_ERROR);
    }
}
```


