package wanda.card.common.provider.entity;

import lombok.Getter;
import lombok.Setter;
import wanda.card.common.contract.constant.OperationLogType;

import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Table
public class RechargeOrderLog {
    /**
     * id
     */
    private Long id;
    /**
     * 时间
     */
    private LocalDateTime createTime;

    /**
     * 操作类型
     */
    private OperationLogType operationType;

    /**
     * 操作日志
     */
    private String operationLog;
}
