package wanda.card.kam.admin.service.manager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class EchoManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(EchoManager.class);

    //@Value("${echo.foo}")
    String foo;

    //@Autowired
    //EchoRepository repository;

    public String getEchoResult(String name) {
        String echoResult = "hello," + name;
        LOGGER.info("echo返回:{}", echoResult);
        return echoResult;
    }
}
