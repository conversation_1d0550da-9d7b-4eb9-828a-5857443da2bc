package wanda.card.kam.admin.service.controller;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.contract.EchoService;
import wanda.card.kam.admin.contract.dto.EchoDto;
import wanda.card.kam.admin.service.manager.EchoManager;

/**
 * 示例里@AllArgsConstructor会自动生成构造函数注入，无需@Autowired
 * <p>
 * But,如果有其他普通成员变量，该方式就不能用了，比如多了一个private int foo;
 */
@RestController
@AllArgsConstructor
public class EchoController implements EchoService {

    EchoManager manager;

    @Override
    public EchoDto.EchoResponse echo(EchoDto.EchoRequest request) {
        EchoDto.EchoResponse response = new EchoDto.EchoResponse();
        response.setResult(manager.getEchoResult(request.getName()));
        return response;
    }
}