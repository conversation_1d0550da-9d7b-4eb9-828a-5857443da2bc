package wanda.card.kam.admin.service.test;

import wanda.card.kam.admin.service.ServiceApplication;
import wanda.card.kam.admin.contract.dto.EchoDto;
import wanda.card.kam.admin.service.controller.EchoController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.stark.core.test.TestBase;

@SpringBootTest(classes = {ServiceApplication.class})
public class EchoServiceTests extends TestBase{

    @Autowired
    private EchoController controller;

    @Test
    public void testEcho() {
        EchoDto.EchoRequest request = new EchoDto.EchoRequest();
        request.setName("everyone!");
        controller.echo(request);
    }
}