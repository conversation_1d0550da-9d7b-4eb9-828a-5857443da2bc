package wanda.card.common.service.test;

import wanda.card.common.service.ServiceApplication;
import wanda.card.common.contract.dto.EchoDto;
import wanda.card.common.service.controller.EchoController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.stark.core.test.TestBase;

@SpringBootTest(classes = {ServiceApplication.class})
public class EchoServiceTests extends TestBase{

    @Autowired
    private EchoController controller;

    @Test
    public void testEcho() {
        EchoDto.EchoRequest request = new EchoDto.EchoRequest();
        request.setName("everyone!");
        controller.echo(request);
    }
}