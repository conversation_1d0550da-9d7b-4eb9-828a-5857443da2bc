<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>wanda.card.kam</groupId>
        <artifactId>card-kam-admin-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>card-kam-admin-service-contract</artifactId>

    <dependencies>
        <!-- 协议包 starter -->
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>wanda-contract-spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>wanda.card.kam</groupId>
            <artifactId>card-kam-common-contract</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>