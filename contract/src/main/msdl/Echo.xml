<?xml version="1.0" encoding="UTF-8"?>
<root xmlns="https://code.wandafilm.com/schema/codegen"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="https://code.wandafilm.com/schema/codegen https://code.wandafilm.com/schema/codegen/stark-codegen-java-1.0.0.xsd"
      package="wanda.card.kam.service.contract">

    <!-- 请使用 stark-codegen 工具生成代码：https://docs.wandatech-dev.com/backend/commons/stark-codegen/ -->
    <services>
        <service name="EchoService" description="示例echo服务" path="/">
            <method name="echo" description="示例echo接口">
                <request>
                    <field name="name" type="String" description="name"/>
                </request>
                <response>
                    <field name="result" type="String" description="result"/>
                </response>
                <!-- 接口异常声明 -->
                <errors>
                    <error name="ECHO_ERROR" code="10001" message="示例接口异常"/>
                </errors>
            </method>
        </service>
    </services>

</root>


