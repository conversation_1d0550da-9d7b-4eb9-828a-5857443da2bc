//DO NOT EDIT! Generated by stark-codegen-java-1.0.0-SNAPSHOT.jar! Changes to this file may cause incorrect behavior and will be lost if the code is regenerated!
package wanda.card.kam.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import wanda.card.kam.admin.contract.dto.EchoDto.EchoRequest;
import wanda.card.kam.admin.contract.dto.EchoDto.EchoResponse;

import javax.validation.Valid;

/**
 * 示例echo服务
 */
@Validated
@FeignClient(contextId = "echoService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface EchoService {

    /**
     * 示例echo接口
     *
     * @param request 请求参数
     * @return 请求响应
     */
    @PostMapping("/echo")
    EchoResponse echo(@Valid @RequestBody EchoRequest request);
}
