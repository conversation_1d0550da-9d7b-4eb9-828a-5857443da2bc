//DO NOT EDIT! Generated by stark-codegen-java-1.0.0-SNAPSHOT.jar! Changes to this file may cause incorrect behavior and will be lost if the code is regenerated!
package wanda.card.kam.admin.contract.error;

import wanda.stark.core.lang.EnumErrorSupport;

public class EchoServiceErrors {

    private EchoServiceErrors() {
    }

    /**
     * 示例echo接口
     */
    public enum EchoError implements EnumErrorSupport {

        /**
         * 示例接口异常
         */
        ECHO_ERROR(10001, "示例接口异常");

        private final int code;

        private final String message;

        EchoError(int code, String message) {
            this.code = code;
            this.message = message;
        }

        @Override
        public int code() {
            return code;
        }

        @Override
        public String message() {
            return message;
        }
    }
}
