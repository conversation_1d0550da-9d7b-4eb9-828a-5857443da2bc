package wanda.card.common.contract.constant;

import lombok.AllArgsConstructor;
import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;

@AllArgsConstructor
public enum OperationLogType implements EnumValueSupport, EnumDisplayNameSupport {

    CREATE_ORDER(1, "创建充值订单"),
    DISABLE_ORDER(2, "作废充值订单"),
    RECHARGE(3, "充值");
    private final int value;
    private final String displayName;

    public static CardRechargeStatus valueOf(int value) {
        return Enums.valueOf(CardRechargeStatus.class, value);
    }

    public static CardRechargeStatus valueOf(int value, boolean throwNotFound) {
        return Enums.valueOf(CardRechargeStatus.class, value, throwNotFound);
    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }
}
