stages:
  - apidocs
  - build
  - package
  - deploy-qas
  - deploy-stg
  - deploy-prd

apidocs:
  image: $CI_REGISTRY/devops/java/apidocs
  stage: apidocs
  only:
    - master
    - develop
    - /feature_.*/
  script:
    - docsctl
  tags:
    - docker

build:
  image: $CI_REGISTRY/devops/java/build
  stage: build
  except:
    - apidocs
  script:
    - build
  artifacts:
    paths:
      - dist
    expire_in: 1 hrs
  tags:
    - docker

package:
  image: $CI_REGISTRY/devops/docker/package
  stage: package
  except:
    - apidocs
  variables:
    GIT_STRATEGY: none
  script:
    - cd dist
    - package
  tags:
    - docker

.deploy_job: &deploy_job
  image: $CI_REGISTRY/devops/docker/deploy
  except:
    - apidocs
  variables:
    GIT_STRATEGY: none
  dependencies: []
  tags:
    - docker

deploy-qas:
  <<: *deploy_job
  stage: deploy-qas
  environment:
    name: wanda-qas
  script:
    - deploy
  when: manual

deploy-stg:
  <<: *deploy_job
  stage: deploy-stg
  environment:
    name: wanda-stg
  script:
    - deploy
  when: manual

deploy-prd:
  <<: *deploy_job
  stage: deploy-prd
  only:
    - master
  environment:
    name: wanda-prd
  script:
    - deploy
  when: manual
